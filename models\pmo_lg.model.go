package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOLGInfo represents project letter of guarantee information
type PMOLGInfo struct {
	BaseModel
	ProjectID string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	Value     float64    `json:"value" gorm:"column:value"`
	StartDate *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate   *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	Fee       float64    `json:"fee" gorm:"column:fee"`
	Interest  float64    `json:"interest" gorm:"column:interest"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOLGInfo) TableName() string {
	return "pmo_lg_info"
}

// PMOLGInfoVersion represents versioned LG information
type PMOLGInfoVersion struct {
	PMOLGInfo
	OriginalID string `json:"lg_info_id" gorm:"column:lg_info_id;type:uuid;index"`
}

func (PMOLGInfoVersion) TableName() string {
	return "pmo_lg_info_versions"
}

func (u *PMOLGInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
