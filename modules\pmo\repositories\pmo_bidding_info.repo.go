package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Bidding Info Repository
var PMOBiddingInfo = repository.Make[models.PMOBiddingInfo]()

func PMOBiddingInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tender_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoByProjectID(projectID string) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOBiddingInfoWithVersions() repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		c.Preload("BiddingInfoVersions")
	}
}
