package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Contract Info Repository
var PMOContractInfo = repository.Make[models.PMOContractInfo]()

func PMOContractInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("signing_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOContractInfoByProjectID(projectID string) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOContractInfoWithVersions() repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		c.Preload("ContractInfoVersions")
	}
}
