package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Bidding Info Version Repository
var PMOBiddingInfoVersion = repository.Make[models.PMOBiddingInfoVersion]()

func PMOBiddingInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoVersionByBiddingInfoID(biddingInfoID string) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if biddingInfoID != "" {
			c.Where("bidding_info_id = ?", biddingInfoID)
		}
	}
}
