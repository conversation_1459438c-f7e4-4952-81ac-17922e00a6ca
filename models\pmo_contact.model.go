package models

// PMOContact represents project contacts
type PMOContact struct {
	BaseModel
	ProjectID string `json:"project_id" gorm:"column:project_id;type:uuid"`
	Fullname  string `json:"fullname" gorm:"column:fullname"`
	Phone     string `json:"phone" gorm:"column:phone"`
	Email     string `json:"email" gorm:"column:email"`
	Detail    string `json:"detail" gorm:"column:detail"`
	Company   string `json:"company" gorm:"column:company"`
	Position  string `json:"position" gorm:"column:position"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOContact) TableName() string {
	return "pmo_contacts"
}
