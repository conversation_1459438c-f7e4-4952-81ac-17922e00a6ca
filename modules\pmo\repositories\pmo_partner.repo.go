package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Partner Repository
var PMOPartner = repository.Make[models.PMOPartner]()

func PMOPartnerOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOPartner] {
	return func(c repository.IRepository[models.PMOPartner]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOPartnerWithProject() repository.Option[models.PMOPartner] {
	return func(c repository.IRepository[models.PMOPartner]) {
		c.Preload("Project")
	}
}

func PMOPartnerByProjectID(projectID string) repository.Option[models.PMOPartner] {
	return func(c repository.IRepository[models.PMOPartner]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}
