package handlers

import (
	"net/http"
	"time"

	"gitlab.finema.co/finema/finework/finework-api/emsgs"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/requests"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOProjectBiddingController struct {
}

// PMO Project Bidding methods
func (m PMOProjectBiddingController) BiddingFind(c core.IHTTPContext) error {
	input := &requests.PMOBiddingFind{}
	if err := c.Bind(input); err != nil {
		return c.JSON(emsgs.InvalidParamsError.GetStatus(), emsgs.InvalidParamsError.JSON())
	}

	if ierr := input.Valid(c); ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	pmoBiddingSvc := services.NewPMOProjectBiddingService(c)
	bidding, err := pmoBiddingSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, bidding)
}

func (m PMOProjectBiddingController) BiddingCreate(c core.IHTTPContext) error {
	input := &requests.PMOBiddingCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	pmoBiddingSvc := services.NewPMOProjectBiddingService(c)
	tenderDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.TenderDate))
	announceDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.AnnounceDate))
	payload := &dto.PMOBiddingCreatePayload{
		ProjectID:    c.Param("id"),
		BiddingType:  utils.ToNonPointer(input.BiddingType),
		BiddingValue: utils.ToNonPointer(input.BiddingValue),
		TenderDate:   &tenderDate,
		TenderEntity: utils.ToNonPointer(input.TenderEntity),
		AnnounceDate: &announceDate,
	}

	bidding, err := pmoBiddingSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, bidding)
}

func (m PMOProjectBiddingController) BiddingVersionsFind(c core.IHTTPContext) error {
	pmoBiddingSvc := services.NewPMOProjectBiddingService(c)
	res, err := pmoBiddingSvc.BiddingVersionsFind(c.Param("id"), c.GetPageOptions())
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, res)
}
