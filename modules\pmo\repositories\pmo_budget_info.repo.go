package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Budget Info Repository
var PMOBudgetInfo = repository.Make[models.PMOBudgetInfo]()

func PMOBudgetInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoByProjectID(projectID string) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}
