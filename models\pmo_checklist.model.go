package models

// PMOChecklistItem represents checklist items
type PMOChecklistItem struct {
	BaseModel
	TabKey    PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail    string    `json:"detail" gorm:"column:detail"`
	IsChecked bool      `json:"is_checked" gorm:"column:is_checked"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`
}

func (PMOChecklistItem) TableName() string {
	return "pmo_checklist_items"
}
