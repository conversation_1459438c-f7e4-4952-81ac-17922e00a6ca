package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Document Item Version Repository
var PMODocumentItemVersion = repository.Make[models.PMODocumentItemVersion]()

func PMODocumentItemVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentItemVersion] {
	return func(c repository.IRepository[models.PMODocumentItemVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("updated_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentItemVersionByOriginalID(originalID string) repository.Option[models.PMODocumentItemVersion] {
	return func(c repository.IRepository[models.PMODocumentItemVersion]) {
		if originalID != "" {
			c.Where("document_item_id = ?", originalID)
		}
	}
}
