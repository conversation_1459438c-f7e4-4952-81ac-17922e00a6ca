package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Document Item Repository
var PMODocumentItem = repository.Make[models.PMODocumentItem]()

func PMODocumentItemOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMODocumentItemWithProject() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("Project")
	}
}

func PMODocumentItemWithGroup() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("Group")
	}
}

func PMODocumentItemWithFile() repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		c.Preload("File")
	}
}

func PMODocumentItemByProjectID(projectID string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMODocumentItemByGroupID(groupID string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if groupID != "" {
			c.Where("group_id = ?", groupID)
		}
	}
}

func PMODocumentItemByTabKey(tabKey string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

func PMODocumentItemByType(docType string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if docType != "" {
			c.Where("type = ?", docType)
		}
	}
}

func PMODocumentItemWithSearch(q string) repository.Option[models.PMODocumentItem] {
	return func(c repository.IRepository[models.PMODocumentItem]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ? OR sharepoint_url ILIKE ?", searchTerm, searchTerm)
	}
}
