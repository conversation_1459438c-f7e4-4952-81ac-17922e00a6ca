package services

import (
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectCommentService interface {
	Create(input *dto.PMOCommentCreatePayload) (*models.PMOComment, core.IError)
	Update(id string, input *dto.PMOCommentUpdatePayload) (*models.PMOComment, core.IError)
	Find(id string) (*models.PMOComment, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOCommentPaginationOptions) (*repository.Pagination[models.PMOComment], core.IError)
	Delete(id string) core.IError

	// PMO Comment Version methods
	VersionsPagination(commentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOCommentVersion], core.IError)
	VersionsFind(versionID string) (*models.PMOCommentVersion, core.IError)
}

type pmoProjectCommentService struct {
	ctx core.IContext
}

// PMO Comment methods implementation
func (s pmoProjectCommentService) Create(input *dto.PMOCommentCreatePayload) (*models.PMOComment, core.IError) {
	comment := &models.PMOComment{
		BaseModel:       models.NewBaseModel(),
		ProjectID:       input.ProjectID,
		UserID:          s.ctx.GetUser().ID,
		Channel:         input.Channel,
		Detail:          input.Detail,
		IsClientFlag:    input.IsClientFlag,
		ParentCommentID: input.ParentCommentID,
		CreatedByID:     utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:     utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOComment(s.ctx).Create(comment)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	version := &models.PMOCommentVersion{
		PMOComment: *comment,
		OriginalID: comment.ID,
	}
	ierr = repositories.PMOCommentVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(comment.ID)
}

func (s pmoProjectCommentService) Update(id string, input *dto.PMOCommentUpdatePayload) (*models.PMOComment, core.IError) {
	comment, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	updateMap := map[string]interface{}{
		"updated_by_id": s.ctx.GetUser().ID,
	}
	if input.Detail != "" {
		updateMap["detail"] = input.Detail
		comment.Detail = input.Detail
	}
	if input.IsClientFlag != nil {
		updateMap["is_client_flag"] = input.IsClientFlag
		comment.IsClientFlag = *input.IsClientFlag
	}

	ierr = repositories.PMOComment(s.ctx).Where("id = ?", id).Updates(updateMap)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	version := &models.PMOCommentVersion{
		PMOComment: *comment,
		OriginalID: comment.ID,
	}

	ierr = repositories.PMOCommentVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(comment.ID)
}

func (s pmoProjectCommentService) Find(id string) (*models.PMOComment, core.IError) {
	return repositories.PMOComment(s.ctx,
		repositories.PMOCommentWithRelations(),
	).FindOne("id = ?", id)
}

func (s pmoProjectCommentService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOCommentPaginationOptions) (*repository.Pagination[models.PMOComment], core.IError) {
	return repositories.PMOComment(s.ctx,
		repositories.PMOCommentOrderBy(pageOptions),
		repositories.PMOCommentWithRelations(),
		repositories.PMOCommentByProjectID(projectID),
		repositories.PMOCommentByChannel(utils.ToNonPointer(options.Channel)),
		repositories.PMOCommentByParentID(utils.ToNonPointer(options.ParentID)),
	).Pagination(pageOptions)
}

func (s pmoProjectCommentService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOComment(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func (s pmoProjectCommentService) VersionsPagination(commentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOCommentVersion], core.IError) {
	return repositories.PMOCommentVersion(s.ctx,
		repositories.PMOCommentVersionOrderBy(pageOptions),
		repositories.PMOCommentVersionByCommentID(commentID),
		repositories.PMOCommentVersionWithRelations(),
	).Pagination(pageOptions)
}

func (s pmoProjectCommentService) VersionsFind(versionID string) (*models.PMOCommentVersion, core.IError) {
	return repositories.PMOCommentVersion(s.ctx, repositories.PMOCommentVersionWithRelations()).FindOne("id = ?", versionID)
}

func NewPMOProjectCommentService(ctx core.IContext) IPMOProjectCommentService {
	return &pmoProjectCommentService{ctx: ctx}
}

