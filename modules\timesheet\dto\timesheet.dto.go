package dto

type TimesheetCreateItem struct {
	SgaID       *string
	ProjectCode *string
	Timing      float64
	Type        string
	LeaveType   *string
	Description *string
	Date        string

	// Relations
	ProjectID   *string
	ProjectName *string
	SgaName     *string
}

type TimesheetCreatePayload struct {
	UserID string
	Items  []TimesheetCreateItem
}

type TimesheetUpdatePayload struct {
	UserID      string
	SgaID       *string
	ProjectCode *string
	Timing      float64
	Type        string
	LeaveType   *string
	Description *string
	Date        string

	// Relations
	ProjectID   *string
	ProjectName *string
	SgaName     *string
}

type TimesheetPaginationOptions struct {
	UserID      *string `json:"user_id"`
	StartDate   *string `json:"start_date"`
	EndDate     *string `json:"end_date"`
	ProjectCode *string `json:"project_code"`
	TeamCode    *string `json:"team_code"`
	SgaID       *string `json:"sga_id"`
	Type        *string `json:"type"`
}

type TimesheetSummaryReportOptions struct {
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	TeamCode  *string `json:"team_code"`
}
