package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

// PMOContractInfo represents project contract information
type PMOContractInfo struct {
	BaseModel
	ProjectID            string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	ContractNo           string     `json:"contract_no" gorm:"column:contract_no"`
	Value                float64    `json:"value" gorm:"column:value"`
	SigningDate          *time.Time `json:"signing_date" gorm:"column:signing_date;type:date"`
	StartDate            *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate              *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationDay          int64      `json:"duration_day" gorm:"column:duration_day"`
	WarrantyDurationDay  int64      `json:"warranty_duration_day" gorm:"column:warranty_duration_day"`
	WarrantyDurationYear int64      `json:"warranty_duration_year" gorm:"column:warranty_duration_year"`
	Prime                string     `json:"prime" gorm:"column:prime"`
	PenaltyFee           float64    `json:"penalty_fee" gorm:"column:penalty_fee"`
	IsLegalizeStamp      bool       `json:"is_legalize_stamp" gorm:"column:is_legalize_stamp"`

	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOContractInfo) TableName() string {
	return "pmo_contract_info"
}

// PMOContractInfoVersion represents versioned contract information
type PMOContractInfoVersion struct {
	PMOContractInfo
	OriginalID string `json:"contract_info_id" gorm:"column:contract_info_id;type:uuid;index"`
}

func (PMOContractInfoVersion) TableName() string {
	return "pmo_contract_info_versions"
}

func (u *PMOContractInfoVersion) BeforeCreate(tx *gorm.DB) (err error) {
	u.ID = utils.GetUUID()
	return
}
