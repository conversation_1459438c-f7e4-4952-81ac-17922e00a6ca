package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOTemplateDocument = repository.Make[models.PMOTemplateDocument]()

func PMOTemplateDocumentOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOTemplateDocument] {
	return func(c repository.IRepository[models.PMOTemplateDocument]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOTemplateDocumentWithSearch(q string) repository.Option[models.PMOTemplateDocument] {
	return func(c repository.IRepository[models.PMOTemplateDocument]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ?", searchTerm)
	}
}

func PMOTemplateDocumentWithTabKeyFilter(tabKey *string) repository.Option[models.PMOTemplateDocument] {
	return func(c repository.IRepository[models.PMOTemplateDocument]) {
		if tabKey != nil && *tabKey != "" {
			c.Where("tab_key = ?", *tabKey)
		}
	}
}
