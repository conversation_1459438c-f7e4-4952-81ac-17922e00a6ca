package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectCompetitorService interface {
	Create(input *dto.PMOCompetitorCreatePayload) (*models.PMOCompetitor, core.IError)
	Update(id string, input *dto.PMOCompetitorUpdatePayload) (*models.PMOCompetitor, core.IError)
	Find(id string) (*models.PMOCompetitor, core.IError)
	Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOCompetitorPaginationOptions) (*repository.Pagination[models.PMOCompetitor], core.IError)
	Delete(id string) core.IError
}

type pmoProjectCompetitorService struct {
	ctx core.IContext
}

// PMO Competitor methods implementation
func (s pmoProjectCompetitorService) Create(input *dto.PMOCompetitorCreatePayload) (*models.PMOCompetitor, core.IError) {
	competitor := &models.PMOCompetitor{
		BaseModel:   models.NewBaseModel(),
		ProjectID:   input.ProjectID,
		Company:     input.Company,
		Detail:      input.Detail,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repositories.PMOCompetitor(s.ctx).Create(competitor)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(competitor.ID)
}

func (s pmoProjectCompetitorService) Update(id string, input *dto.PMOCompetitorUpdatePayload) (*models.PMOCompetitor, core.IError) {
	_, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	competitor := &models.PMOCompetitor{
		Company:     input.Company,
		Detail:      input.Detail,
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOCompetitor(s.ctx).Where("id = ?", id).Updates(competitor)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(id)
}

func (s pmoProjectCompetitorService) Find(id string) (*models.PMOCompetitor, core.IError) {
	return repositories.PMOCompetitor(s.ctx,
		repositories.PMOCompetitorWithProject(),
	).FindOne("id = ?", id)
}

func (s pmoProjectCompetitorService) Pagination(projectID string, pageOptions *core.PageOptions, options *dto.PMOCompetitorPaginationOptions) (*repository.Pagination[models.PMOCompetitor], core.IError) {
	return repositories.PMOCompetitor(s.ctx,
		repositories.PMOCompetitorOrderBy(pageOptions),
		repositories.PMOCompetitorWithProject(),
		repositories.PMOCompetitorByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s pmoProjectCompetitorService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOCompetitor(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOProjectCompetitorService(ctx core.IContext) IPMOProjectCompetitorService {
	return &pmoProjectCompetitorService{ctx: ctx}
}
