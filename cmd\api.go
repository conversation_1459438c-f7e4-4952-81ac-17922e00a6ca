package cmd

import (
	"fmt"
	"os"

	"gitlab.finema.co/finema/finework/finework-api/modules/home"

	checkinHandlers "gitlab.finema.co/finema/finework/finework-api/modules/checkin/handlers"
	departmentHandlers "gitlab.finema.co/finema/finework/finework-api/modules/department/handlers"
	holidayHandlers "gitlab.finema.co/finema/finework/finework-api/modules/holiday/handlers"
	ministryHandlers "gitlab.finema.co/finema/finework/finework-api/modules/ministry/handlers"
	pmoHandlers "gitlab.finema.co/finema/finework/finework-api/modules/pmo/handlers"
	projectHandlers "gitlab.finema.co/finema/finework/finework-api/modules/project/handlers"
	sgaHandlers "gitlab.finema.co/finema/finework/finework-api/modules/sga/handlers"
	teamHandlers "gitlab.finema.co/finema/finework/finework-api/modules/team/handlers"
	timesheetHandlers "gitlab.finema.co/finema/finework/finework-api/modules/timesheet/handlers"
	uploadHandlers "gitlab.finema.co/finema/finework/finework-api/modules/upload/handlers"
	userHandlers "gitlab.finema.co/finema/finework/finework-api/modules/user/handlers"

	core "gitlab.finema.co/finema/idin-core"
)

func APIRun() {
	os.Setenv("TZ", "Asia/Bangkok")

	env := core.NewEnv()
	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Postgres: %v \n", err)
		os.Exit(1)
	}

	e := core.NewHTTPServer(&core.HTTPContextOptions{
		ContextOptions: &core.ContextOptions{
			DB:  db,
			ENV: env,
		},
	})

	//e.Pre(middleware.Rewrite(map[string]string{
	//	"/api/*": "/$1",
	//}))

	home.NewHomeHTTP(e)

	// Register modules
	userHandlers.NewAuthHTTP(e)
	userHandlers.NewMeHTTP(e)
	userHandlers.NewUserHTTP(e)
	holidayHandlers.NewHolidayHTTP(e)
	teamHandlers.NewTeamHTTP(e)
	sgaHandlers.NewSgaHTTP(e)
	projectHandlers.NewProjectHTTP(e)
	timesheetHandlers.NewTimesheetHTTP(e)
	checkinHandlers.NewCheckinHTTP(e)
	ministryHandlers.NewMinistryHTTP(e)
	departmentHandlers.NewDepartmentHTTP(e)
	pmoHandlers.NewPMOProjectHTTP(e)
	pmoHandlers.NewPMOTemplateHTTP(e)
	uploadHandlers.NewUploadHTTP(e)
	core.StartHTTPServer(e, env)
}
