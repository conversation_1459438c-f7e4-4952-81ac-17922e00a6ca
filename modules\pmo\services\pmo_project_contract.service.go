package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectContractService interface {
	Create(input *dto.PMOContractCreatePayload) (*models.PMOContractInfo, core.IError)
	Find(projectID string) (*models.PMOContractInfo, core.IError)
	ContractVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOContractInfoVersion], core.IError)
}

type pmoProjectContractService struct {
	ctx core.IContext
}

// PMO Contract methods implementation
func (s pmoProjectContractService) Create(input *dto.PMOContractCreatePayload) (*models.PMOContractInfo, core.IError) {
	// Check if contract already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	contract := &models.PMOContractInfo{
		BaseModel:            models.NewBaseModel(),
		ProjectID:            input.ProjectID,
		ContractNo:           input.ContractNo,
		Value:                input.Value,
		SigningDate:          input.SigningDate,
		StartDate:            input.StartDate,
		EndDate:              input.EndDate,
		DurationDay:          input.DurationDay,
		WarrantyDurationDay:  input.WarrantyDurationDay,
		WarrantyDurationYear: input.WarrantyDurationYear,
		Prime:                input.Prime,
		PenaltyFee:           input.PenaltyFee,
		IsLegalizeStamp:      input.IsLegalizeStamp,
		CreatedByID:          utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:          utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOContractInfo(s.ctx).Create(contract)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOContractInfoVersion{
		PMOContractInfo: *contract,
		OriginalID:      contract.ID,
	}

	ierr = repositories.PMOContractInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(contract.ProjectID)
}

func (s pmoProjectContractService) update(existing *models.PMOContractInfo, input *dto.PMOContractCreatePayload) (*models.PMOContractInfo, core.IError) {
	// Update the main record
	existing.ContractNo = input.ContractNo
	existing.Value = input.Value
	existing.SigningDate = input.SigningDate
	existing.StartDate = input.StartDate
	existing.EndDate = input.EndDate
	existing.DurationDay = input.DurationDay
	existing.WarrantyDurationDay = input.WarrantyDurationDay
	existing.WarrantyDurationYear = input.WarrantyDurationYear
	existing.Prime = input.Prime
	existing.PenaltyFee = input.PenaltyFee
	existing.IsLegalizeStamp = input.IsLegalizeStamp
	existing.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)
	existing.UpdatedAt = utils.GetCurrentDateTime()

	ierr := repositories.PMOContractInfo(s.ctx).Where("id = ?", existing.ID).Updates(existing)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOContractInfoVersion{
		PMOContractInfo: *existing,
		OriginalID:      existing.ID,
	}

	ierr = repositories.PMOContractInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectContractService) Find(projectID string) (*models.PMOContractInfo, core.IError) {
	return repositories.PMOContractInfo(s.ctx).FindOne("project_id = ?", projectID)
}

func (s pmoProjectContractService) ContractVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOContractInfoVersion], core.IError) {
	// First get the contract info to get the original ID
	contract, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOContractInfoVersion(s.ctx,
		repositories.PMOContractInfoVersionOrderBy(pageOptions),
		repositories.PMOContractInfoVersionByContractInfoID(contract.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectContractService(ctx core.IContext) IPMOProjectContractService {
	return &pmoProjectContractService{ctx: ctx}
}
