package repositories

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

var PMOProject = repository.Make[models.PMOProject]()

func PMOProjectOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOProjectWithSearch(q string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if q == "" {
			return
		}
		searchTerm := "%" + q + "%"
		c.Where("name ILIKE ? OR slug ILIKE ? OR email ILIKE ?", searchTerm, searchTerm, searchTerm)
	}
}

func PMOProjectWithStatus(status string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if status == "" {
			return
		}
		c.Where("status = ?", status)
	}
}

func PMOProjectWithProject() repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		c.Preload("Project")
	}
}

func PMOProjectWithCurrentPermission(userID string) repository.Option[models.PMOProject] {
	return func(c repository.IRepository[models.PMOProject]) {
		if userID == "" {
			return
		}
		c.Preload("Permission", "user_id = ?", userID)
	}
}
