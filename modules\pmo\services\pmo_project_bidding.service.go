package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/dto"
	"gitlab.finema.co/finema/finework/finework-api/modules/pmo/repositories"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/errmsgs"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOProjectBiddingService interface {
	Create(input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError)
	Find(projectID string) (*models.PMOBiddingInfo, core.IError)
	BiddingVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBiddingInfoVersion], core.IError)
}

type pmoProjectBiddingService struct {
	ctx core.IContext
}

// PMO Bidding methods implementation
func (s pmoProjectBiddingService) Create(input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError) {
	// Check if bidding already exists for this project
	existing, ierr := s.Find(input.ProjectID)
	if ierr != nil && !errmsgs.IsNotFoundError(ierr) {
		return nil, s.ctx.NewError(ierr, ierr)
	}
	if existing != nil {
		// If exists, update it instead by creating a new version and updating the main record
		return s.update(existing, input)
	}

	bidding := &models.PMOBiddingInfo{
		BaseModel:    models.NewBaseModel(),
		ProjectID:    input.ProjectID,
		BiddingType:  input.BiddingType,
		BiddingValue: input.BiddingValue,
		TenderDate:   input.TenderDate,
		TenderEntity: input.TenderEntity,
		AnnounceDate: input.AnnounceDate,
		CreatedByID:  utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:  utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr = repositories.PMOBiddingInfo(s.ctx).Create(bidding)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create initial version
	version := &models.PMOBiddingInfoVersion{
		PMOBiddingInfo: *bidding,
		OriginalID:     bidding.ID,
	}

	ierr = repositories.PMOBiddingInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(bidding.ProjectID)
}

func (s pmoProjectBiddingService) update(existing *models.PMOBiddingInfo, input *dto.PMOBiddingCreatePayload) (*models.PMOBiddingInfo, core.IError) {
	// Update the main record
	existing.BiddingType = input.BiddingType
	existing.BiddingValue = input.BiddingValue
	existing.TenderDate = input.TenderDate
	existing.TenderEntity = input.TenderEntity
	existing.AnnounceDate = input.AnnounceDate
	existing.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)
	existing.UpdatedAt = utils.GetCurrentDateTime()

	ierr := repositories.PMOBiddingInfo(s.ctx).Where("id = ?", existing.ID).Updates(existing)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Create version from current data before updating
	version := &models.PMOBiddingInfoVersion{
		PMOBiddingInfo: *existing,
		OriginalID:     existing.ID,
	}

	ierr = repositories.PMOBiddingInfoVersion(s.ctx).Create(version)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(existing.ProjectID)
}

func (s pmoProjectBiddingService) Find(projectID string) (*models.PMOBiddingInfo, core.IError) {
	return repositories.PMOBiddingInfo(s.ctx).FindOne("project_id = ?", projectID)
}

func (s pmoProjectBiddingService) BiddingVersionsFind(projectID string, pageOptions *core.PageOptions) (*repository.Pagination[models.PMOBiddingInfoVersion], core.IError) {
	// First get the bidding info to get the original ID
	bidding, ierr := s.Find(projectID)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return repositories.PMOBiddingInfoVersion(s.ctx,
		repositories.PMOBiddingInfoVersionOrderBy(pageOptions),
		repositories.PMOBiddingInfoVersionByBiddingInfoID(bidding.ID),
	).Pagination(pageOptions)
}

func NewPMOProjectBiddingService(ctx core.IContext) IPMOProjectBiddingService {
	return &pmoProjectBiddingService{ctx: ctx}
}
